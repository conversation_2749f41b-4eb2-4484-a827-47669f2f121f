import React, { useState, useEffect, useCallback } from 'react';
import { z } from 'zod';
import {
  ChevronLeft,
  ChevronRight,
  Plus,
  Trash2,
  Check,
  X,
  Upload,
} from 'lucide-react';
import { toast } from 'react-toastify';
import { useDropzone } from 'react-dropzone';

import PrimaryButton from '@/components/common/button/primaryButton';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import { Textarea } from '@/components/common/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import {
  IOption,
  ReactSelectMulti,
  ReactSelectSingle,
} from '@/components/common/multiSelectInput';
import Calendar from '@/components/common/calendar';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import useValidators from '@/hooks/useValidator';
import { ToggleSwitch } from '@/components/common/toogleSwitch';
import Tabs from '@/components/common/tabs';
import SecondaryButton from '../common/button/secondaryButton';
import { cn } from '@/utils/styleUtils';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import axios from 'axios';
import useFetch from '@/hooks/useFetch';
import FileCard from '../common/modals/uploadModal/fileCard';
import { v4 as uuidv4 } from 'uuid';

// Validation schemas
const optionSchema = z.object({
  label: z.string(),
  value: z.string(),
});

const questionSchema = z.object({
  description: z.string().min(1, 'Question description is required'),
  type: z.enum([
    'multiple_choice',
    'true_false',
    'short_answer',
    'fill_in_the_blanks',
  ]),
  options: z.array(z.string()).optional(),
  correctAnswers: z.array(z.number()).optional(),
  correctAnswer: z.string().optional(),
  blanks: z.array(z.string()).optional(),
  points: z.number().min(1, 'Points must be at least 1').default(1),
});

const trainingSchema = {
  title: z.string().min(1, 'Training title is required'),
  description: z.string().optional(),
  training_type: z.string().min(1, 'Training type is required'),
  category: z.string().min(1, 'Category is required'),
  file_path: z.string().optional(),
  file_extension: z.string().optional(),
  passing_score_in_percent: z.number().min(0).max(100).optional(),
  questions: z.array(questionSchema).optional(),
  training_assignments: z.array(z.string()).optional(),
  is_publish: z.boolean().default(false),
};

// Fixed Question interface with proper types
interface Question {
  id: string;
  description: string;
  type: 'MultipleChoice' | 'Boolean' | 'FillInBlank';
  options: string[];
  correctAnswer: string;
  correctAnswers: number[]; // Fixed: should be number[] not string[]
  sampleAnswer?: string;
  explanation?: string;
  points: number;
  blanks: string[]; // Added blanks property for fill-in-the-blanks
}

interface TrainingFormData {
  title: string;
  description: string;
  training_type: string;
  category: any;
  file_path: string;
  file_extension: string;
  passing_score_in_percent: number;
  questions: Question[];
  training_assignments: string[];
  is_publish: boolean;
  // UI-only fields
  tags: IOption[];
  objectives: string;
  startDate: string;
  supervisor: string;
  includeQuiz: boolean;
  contentFiles: File[];
  applicableRoles: string[];
}

interface CreateTrainingModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
  refetch: () => void;
}

const CreateTrainingModal: React.FC<CreateTrainingModalProps> = ({
  open,
  onOpenChange,
  onSuccess,
  refetch,
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const { postData, isLoading, response, error } = usePost();
  const {
    data: categories,
    isLoading: categoriesLoading,
    error: categoriesError,
  } = useFetch<any>(accessToken, 'training/categories', {});

  const [activeTab, setActiveTab] = useState(0);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showValidationErrors, setShowValidationErrors] = useState(false);
  const [isDraftSave, setIsDraftSave] = useState(false);
  const [uploadingFile, setUploadingFile] = useState(false);

  const [formData, setFormData] = useState<TrainingFormData>({
    title: '',
    description: '',
    training_type: 'document',
    category: '',
    file_path: '',
    file_extension: '',
    passing_score_in_percent: 0,
    questions: [],
    training_assignments: [],
    is_publish: false,
    // UI-only fields
    tags: [],
    objectives: '',
    startDate: '',
    supervisor: '',
    includeQuiz: false,
    contentFiles: [],
    applicableRoles: [],
  });

  // File upload configuration
  const acceptFileTypes = {
    'application/pdf': ['.pdf'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
      '.docx',
    ],
    'application/msword': ['.doc'],
    'video/mp4': ['.mp4'],
    'video/avi': ['.avi'],
    'video/mov': ['.mov'],
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    // Only take the first file
    setFormData((prev) => ({
      ...prev,
      contentFiles: [acceptedFiles[0]],
    }));
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: acceptFileTypes,
    multiple: false, // Changed to false
  });

  const removeFile = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      contentFiles: prev.contentFiles.filter((_, i) => i !== index),
    }));
  };

  const { validationErrors, startValidation, reset } = useValidators({
    schemas: trainingSchema,
    values: formData,
  });

  // Updated content types
  const contentTypes = [{ label: 'PDF', value: 'document' }];

  const supervisors = [
    { label: 'John Doe', value: 'john-doe' },
    { label: 'Jane Smith', value: 'jane-smith' },
    { label: 'Mike Johnson', value: 'mike-johnson' },
  ];

  const availableTags = [
    { label: 'Safety', value: 'safety' },
    { label: 'Compliance', value: 'compliance' },
    { label: 'Technical', value: 'technical' },
    { label: 'Soft Skills', value: 'soft-skills' },
  ];

  const availableRoles = [
    { label: 'Quality Manager', value: 'quality-manager' },
    { label: 'Production Supervisor', value: 'production-supervisor' },
    { label: 'Safety Officer', value: 'safety-officer' },
    { label: 'Compliance Specialist', value: 'compliance-specialist' },
    { label: 'Operations Manager', value: 'operations-manager' },
    { label: 'Technical Lead', value: 'technical-lead' },
    { label: 'HR Representative', value: 'hr-representative' },
    { label: 'Auditor', value: 'auditor' },
    { label: 'Training Coordinator', value: 'training-coordinator' },
    { label: 'All Employees', value: 'all-employees' },
  ];

  const handleRoleChange = (roleValue: string, isChecked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      applicableRoles: isChecked
        ? [...prev.applicableRoles, roleValue]
        : prev.applicableRoles.filter((role) => role !== roleValue),
    }));
  };

  const addQuestion = () => {
    const newQuestion: Question = {
      id: Date.now().toString(),
      description: '',
      type: 'MultipleChoice',
      options: [''],
      correctAnswer: '',
      correctAnswers: [], // Fixed: initialize as empty array
      sampleAnswer: '',
      explanation: '',
      points: 1,
      blanks: [], // Added blanks initialization
    };
    setFormData((prev) => ({
      ...prev,
      questions: [...prev.questions, newQuestion],
    }));
  };

  const removeQuestion = (questionId: string) => {
    setFormData((prev) => ({
      ...prev,
      questions: prev.questions.filter((q) => q.id !== questionId),
    }));
  };

  const updateQuestion = (questionId: string, updates: Partial<Question>) => {
    setFormData((prev) => ({
      ...prev,
      questions: prev.questions.map((q) =>
        q.id === questionId ? { ...q, ...updates } : q,
      ),
    }));
  };

  const addOption = (questionId: string) => {
    const question = formData.questions.find((q) => q.id === questionId);
    if (!question) return;

    updateQuestion(questionId, {
      options: [...question.options, ''],
    });
  };

  const updateOption = (
    questionId: string,
    optionIndex: number,
    value: string,
  ) => {
    const question = formData.questions.find((q) => q.id === questionId);
    if (!question) return;

    const newOptions = [...question.options];
    newOptions[optionIndex] = value;
    updateQuestion(questionId, { options: newOptions });
  };

  const removeOption = (questionId: string, optionIndex: number) => {
    const question = formData.questions.find((q) => q.id === questionId);
    if (!question) return;

    const newOptions = question.options.filter(
      (_, index) => index !== optionIndex,
    );
    const newCorrectAnswers =
      question.correctAnswers
        ?.filter((index) => index !== optionIndex)
        .map((index) => (index > optionIndex ? index - 1 : index)) || [];

    updateQuestion(questionId, {
      options: newOptions,
      correctAnswers: newCorrectAnswers,
    });
  };

  const toggleCorrectAnswer = (questionId: string, optionIndex: number) => {
    const question = formData.questions.find((q) => q.id === questionId);
    if (!question) return;

    const correctAnswers = question.correctAnswers || [];
    const isCorrect = correctAnswers.includes(optionIndex);
    const newCorrectAnswers = isCorrect
      ? correctAnswers.filter((index) => index !== optionIndex)
      : [...correctAnswers, optionIndex];

    updateQuestion(questionId, { correctAnswers: newCorrectAnswers });
  };

  // Fixed: Add functions for managing blanks in fill-in-the-blanks questions
  const addBlank = (questionId: string) => {
    const question = formData.questions.find((q) => q.id === questionId);
    if (!question) return;

    const newBlanks = [...(question.blanks || []), ''];
    updateQuestion(questionId, {
      blanks: newBlanks,
    });
  };

  const updateBlank = (
    questionId: string,
    blankIndex: number,
    value: string,
  ) => {
    const question = formData.questions.find((q) => q.id === questionId);
    if (!question) return;

    const newBlanks = [...(question.blanks || [])];
    newBlanks[blankIndex] = value;
    updateQuestion(questionId, { blanks: newBlanks });
  };

  const removeBlank = (questionId: string, blankIndex: number) => {
    const question = formData.questions.find((q) => q.id === questionId);
    if (!question) return;

    const newBlanks = (question.blanks || []).filter(
      (_, index) => index !== blankIndex,
    );
    updateQuestion(questionId, { blanks: newBlanks });
  };

  const tabsData = [
    { name: 'Basic Info', textColor: 'text-dark-100' },
    {
      name: 'Quiz',
      textColor: formData.includeQuiz ? 'text-dark-100' : 'text-gray-400',
      disabled: !formData.includeQuiz,
    },
    { name: 'Content', textColor: 'text-dark-100' },
    { name: 'Preview', textColor: 'text-dark-100' },
  ];

  // Fixed: Improved validation for current tab
  const validateCurrentTab = async () => {
    if (activeTab === 0) {
      const { hasValidationErrors } = await startValidation();
      if (hasValidationErrors) {
        setShowValidationErrors(true);
      }
      return !hasValidationErrors;
    }
    if (activeTab === 1 && formData.includeQuiz) {
      const isValid = formData.questions.every((q) => {
        // Check if question description is filled
        if (q.description.trim() === '') return false;

        // Check validation based on question type
        if (q.type === 'MultipleChoice') {
          return (
            q.options.some((opt) => opt.trim() !== '') &&
            q.correctAnswer.trim() !== ''
          );
        }
        if (q.type === 'Boolean') {
          return q.correctAnswer.trim() !== '';
        }
        if (q.type === 'FillInBlank') {
          // Fixed: Check if at least one blank is filled AND all filled blanks have values
          const hasValidBlanks =
            q.blanks &&
            q.blanks.length > 0 &&
            q.blanks.every((blank) => blank.trim() !== '');
          const hasSingleAnswer =
            q.correctAnswer && q.correctAnswer.trim() !== '';
          return hasValidBlanks || hasSingleAnswer;
        }
        return true;
      });

      if (!isValid) {
        toast.error('Please complete all quiz questions before proceeding');
      }
      return isValid;
    }
    return true;
  };

  const handleFileUpload = async (
    file: File,
    id: string,
  ): Promise<{ file_path: string; file_extension: string } | null> => {
    try {
      setUploadingFile(true);
      const formData = new FormData();
      formData.append('file', file);

      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;

      const orgId =
        typeof window !== 'undefined'
          ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
          : null;

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${accessToken}`,
          ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
        },
      };
      const url = `${baseUrl}/${productVersion}/file/upload?document_for=people_hub&sub_path=/${`people/training/${id}/v1`}`;
      const response = await axios.post(url, formData, config);

      setUploadingFile(false);
      return {
        file_path: response.data.file_path,
        file_extension: response.data.file_ext,
      };
    } catch (error) {
      setUploadingFile(false);
      console.error('Error uploading file:', error);
      toast.error('File upload failed');
      return null;
    }
  };

  const handleSubmit = async (isDraft = false) => {
    setIsDraftSave(isDraft);
    const isValid = await validateCurrentTab();
    const id = uuidv4();

    if (!isValid) return;

    // Handle file upload first if there are files
    let uploadedFilePath = '';
    let fileExtension = '';

    if (formData.contentFiles.length > 0) {
      const file = formData.contentFiles[0]; // Take first file
      const uploadResult = await handleFileUpload(file, id);
      if (!uploadResult) {
        toast.error('File upload failed');
        return;
      }
      uploadedFilePath = uploadResult.file_path;
      fileExtension = uploadResult.file_extension;
    }

    const payload = {
      id: id,
      title: formData.title,
      description: formData.description,
      training_type: formData.training_type,
      category: formData.category,
      file_path: uploadedFilePath,
      file_extension: fileExtension,
      passing_score_in_percent: formData.includeQuiz
        ? formData.passing_score_in_percent
        : 0,
      questions: formData.includeQuiz
        ? formData.questions.map((q, index) => ({
            question_text: q.description,
            question_type: q.type,
            points: q.points,
            options:
              q.type === 'MultipleChoice' || q.type === 'Boolean'
                ? q.options
                : undefined,
            correct_answer:
              q.type === 'FillInBlank'
                ? q.blanks && q.blanks.length > 0
                  ? q.blanks.join('|')
                  : q.correctAnswer
                : q.correctAnswer,
            blanks: q.type === 'FillInBlank' ? q.blanks : undefined, // Added blanks to payload
            sequence_number: index + 1,
          }))
        : [],
      training_assignments: [],
      is_publish: !isDraft,
    };

    console.log('payload', payload);

    if (accessToken) {
      await postData(accessToken, 'trainings', payload);
    }
  };

  // Helper function to format correct answer as string
  const getCorrectAnswerString = (question: Question): string => {
    return question.correctAnswer || '';
  };

  const handleTabChange = (value: React.SetStateAction<number>) => {
    const newTab = typeof value === 'function' ? value(activeTab) : value;

    // Skip quiz tab if quiz is not included
    if (newTab === 1 && !formData.includeQuiz) {
      return;
    }
    setActiveTab(newTab);
  };

  useEffect(() => {
    if (response) {
      setShowConfirmation(true);
      const successMessage = isDraftSave
        ? 'Training module saved as draft successfully!'
        : 'Training module created and published successfully!';

      setShowConfirmation(false);
      onOpenChange(false);
      onSuccess?.();
      toast.success(successMessage);
      refetch();
    }
  }, [response, isDraftSave, onOpenChange, onSuccess]);

  const renderStep1 = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="md:col-span-2">
          <Label
            htmlFor="title"
            className="text-base font-medium leading-6 text-dark-100 mb-2 block"
          >
            Training Title<span className="text-red-200">*</span>
          </Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, title: e.target.value }))
            }
            placeholder="Enter training title"
            className={
              showValidationErrors && validationErrors.title
                ? 'border-red-500'
                : ''
            }
          />
          {showValidationErrors && validationErrors.title && (
            <p className="text-red-500 text-sm mt-1">
              {validationErrors.title}
            </p>
          )}
        </div>

        <div className="md:col-span-2">
          <Label
            htmlFor="description"
            className="text-base font-medium leading-6 text-dark-100 mb-2 block"
          >
            Description
          </Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, description: e.target.value }))
            }
            placeholder="Enter training description"
            rows={3}
          />
        </div>

        <div>
          <Label
            htmlFor="training_type"
            className="text-base font-medium leading-6 text-dark-100 mb-2 block"
          >
            Training Type<span className="text-red-200">*</span>
          </Label>
          <Select
            value={formData.training_type}
            onValueChange={(value) =>
              setFormData((prev) => ({ ...prev, training_type: value }))
            }
          >
            <SelectTrigger
              className={
                showValidationErrors && validationErrors.training_type
                  ? 'border-red-500'
                  : ''
              }
            >
              <SelectValue placeholder="Select training type" />
            </SelectTrigger>
            <SelectContent>
              {contentTypes.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {showValidationErrors && validationErrors.training_type && (
            <p className="text-red-500 text-sm mt-1">
              {validationErrors.training_type}
            </p>
          )}
        </div>

        <div>
          <Label
            htmlFor="category"
            className="text-base font-medium leading-6 text-dark-100 mb-2 block"
          >
            Category<span className="text-red-200">*</span>
          </Label>
          <Select
            value={formData.category}
            onValueChange={(value) =>
              setFormData((prev) => ({ ...prev, category: value }))
            }
          >
            <SelectTrigger
              className={
                showValidationErrors && validationErrors.category
                  ? 'border-red-500'
                  : ''
              }
            >
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {categories?.records?.map((cat: any) => (
                <SelectItem key={cat.id} value={cat.id}>
                  {cat.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {showValidationErrors && validationErrors.category && (
            <p className="text-red-500 text-sm mt-1">
              {validationErrors.category}
            </p>
          )}
        </div>
      </div>

      <div className="flex items-center justify-between">
        <ToggleSwitch
          initialState={formData.includeQuiz}
          className="gap-2 mb-4"
          onChange={(isActive) =>
            setFormData((prev) => ({
              ...prev,
              includeQuiz: isActive,
            }))
          }
          label="Include Quiz"
          labelPosition="right"
        />

        <div
          className={`mb-4 flex items-center space-x-2 transition-opacity ${
            formData.includeQuiz
              ? 'opacity-100'
              : 'opacity-0 pointer-events-none'
          }`}
        >
          <Label
            htmlFor="passing_score"
            className="text-base font-medium leading-6 text-dark-100 mb-2 block"
          >
            Passing Score (%)<span className="text-red-200">*</span>
          </Label>

          <Input
            id="passing_score"
            type="number"
            min="0"
            max="100"
            value={formData.passing_score_in_percent || ''}
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                passing_score_in_percent: parseInt(e.target.value) || 70,
              }))
            }
            placeholder="70"
            className="w-32"
          />
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-base font-medium">Quiz Configuration</h3>
        <div className="flex items-center space-x-3">
          <SecondaryButton
            text="+ Multiple Choice"
            size="medium"
            onClick={() => {
              const newQuestion: Question = {
                id: `new_${Date.now()}`,
                description: '',
                type: 'MultipleChoice',
                options: [''],
                correctAnswers: [],
                correctAnswer: '',
                sampleAnswer: '',
                explanation: '',
                points: 1,
                blanks: [],
              };
              setFormData((prev) => ({
                ...prev,
                questions: [...prev.questions, newQuestion],
              }));
            }}
          />
          <SecondaryButton
            text="+ True/False"
            size="medium"
            onClick={() => {
              const newQuestion: Question = {
                id: `new_${Date.now()}`,
                description: '',
                type: 'Boolean',
                options: ['True', 'False'],
                correctAnswers: [],
                correctAnswer: '',
                sampleAnswer: '',
                explanation: '',
                points: 1,
                blanks: [],
              };
              setFormData((prev) => ({
                ...prev,
                questions: [...prev.questions, newQuestion],
              }));
            }}
          />
          <SecondaryButton
            text="+ Fill in Blanks"
            size="medium"
            onClick={() => {
              const newQuestion: Question = {
                id: `new_${Date.now()}`,
                description: '',
                type: 'FillInBlank',
                options: [''],
                correctAnswers: [],
                correctAnswer: '',
                sampleAnswer: '',
                explanation: '',
                points: 1,
                blanks: [''],
              };
              setFormData((prev) => ({
                ...prev,
                questions: [...prev.questions, newQuestion],
              }));
            }}
          />
        </div>
      </div>

      {/* Question rendering logic */}
      {formData.questions.map((question, questionIndex) => (
        <div key={question.id} className="border rounded-lg p-6 space-y-4">
          <div className="flex justify-between items-start">
            <h4 className="text-lg font-medium">
              Question {questionIndex + 1}
            </h4>
            <button
              onClick={() => removeQuestion(question.id)}
              className="text-red-500 hover:text-red-700"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>

          <div>
            <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
              Question Type
            </Label>
            <Select
              value={question.type}
              onValueChange={(
                value: 'MultipleChoice' | 'Boolean' | 'FillInBlank',
              ) =>
                updateQuestion(question.id, {
                  type: value,
                  options: value === 'Boolean' ? ['True', 'False'] : [''],
                  correctAnswers: [],
                  correctAnswer: '',
                  sampleAnswer: '',
                  explanation: '',
                  blanks: value === 'FillInBlank' ? [''] : [],
                })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="MultipleChoice">Multiple Choice</SelectItem>
                <SelectItem value="Boolean">True/False</SelectItem>
                <SelectItem value="FillInBlank">Fill in Blanks</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Question description */}
          <div>
            <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
              Question<span className="text-red-200">*</span>
            </Label>
            <Textarea
              value={question.description}
              onChange={(e) =>
                updateQuestion(question.id, { description: e.target.value })
              }
              placeholder="Enter your question"
              rows={3}
            />
          </div>

          {/* Multiple Choice Options */}
          {question.type === 'MultipleChoice' && (
            <div>
              <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
                Options<span className="text-red-200">*</span>
              </Label>
              <div className="space-y-2">
                {question.options.map((option, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Input
                      value={option}
                      onChange={(e) =>
                        updateOption(question.id, index, e.target.value)
                      }
                      placeholder={`Option ${index + 1}`}
                      className="flex-1"
                    />
                    <input
                      type="radio"
                      name={`correct-${question.id}`}
                      checked={question.correctAnswer === option}
                      onChange={() =>
                        updateQuestion(question.id, { correctAnswer: option })
                      }
                      className="w-4 h-4"
                    />
                    <label className="text-sm">Correct</label>
                    {question.options.length > 1 && (
                      <button
                        onClick={() => removeOption(question.id, index)}
                        className="text-red-500 hover:text-red-700 text-sm"
                      >
                        Remove
                      </button>
                    )}
                  </div>
                ))}
                <button
                  onClick={() => addOption(question.id)}
                  className="text-blue-500 hover:text-blue-700 text-sm"
                >
                  + Add Option
                </button>
              </div>
            </div>
          )}

          {/* Boolean (True/False) */}
          {question.type === 'Boolean' && (
            <div>
              <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
                Correct Answer
              </Label>
              <div className="grid grid-cols-2 gap-4">
                {['True', 'False'].map((answer) => (
                  <div
                    key={answer}
                    className={`p-4 border rounded-lg cursor-pointer ${
                      question.correctAnswer === answer
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 hover:bg-gray-50'
                    }`}
                    onClick={() =>
                      updateQuestion(question.id, { correctAnswer: answer })
                    }
                  >
                    <div className="text-center font-medium">{answer}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Fixed Fill in Blanks - Support for multiple blanks */}
          {question.type === 'FillInBlank' && (
            <div className="space-y-4">
              <div>
                <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
                  Instructions
                </Label>
                <p className="text-sm text-gray-600 mb-3">
                  Use [...] in your question text to indicate where blanks
                  should appear. Add multiple correct answers below for each
                  blank.
                </p>
              </div>

              <div>
                <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
                  Correct Answers for Blanks
                  <span className="text-red-200">*</span>
                </Label>
                <div className="space-y-2">
                  {(question.blanks || []).map((blank, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <span className="text-sm font-medium w-16">
                        Blank {index + 1}:
                      </span>
                      <Input
                        value={blank}
                        onChange={(e) =>
                          updateBlank(question.id, index, e.target.value)
                        }
                        placeholder={`Answer for blank ${index + 1}`}
                        className="flex-1"
                      />
                      {(question.blanks || []).length > 1 && (
                        <button
                          onClick={() => removeBlank(question.id, index)}
                          className="text-red-500 hover:text-red-700 text-sm"
                        >
                          Remove
                        </button>
                      )}
                    </div>
                  ))}
                  <button
                    onClick={() => addBlank(question.id)}
                    className="text-blue-500 hover:text-blue-700 text-sm"
                  >
                    + Add Another Blank
                  </button>
                </div>
              </div>

              {/* Fallback single answer field if no blanks are defined */}
              {(!question.blanks || question.blanks.length === 0) && (
                <div>
                  <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
                    Single Correct Answer<span className="text-red-200">*</span>
                  </Label>
                  <Input
                    value={question.correctAnswer || ''}
                    onChange={(e) =>
                      updateQuestion(question.id, {
                        correctAnswer: e.target.value,
                      })
                    }
                    placeholder="Enter correct answer"
                    className="w-full"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Use this if you prefer a single answer instead of multiple
                    blanks
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Points */}
          <div>
            <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
              Points
            </Label>
            <Input
              type="number"
              min="1"
              value={question.points}
              onChange={(e) =>
                updateQuestion(question.id, {
                  points: parseInt(e.target.value) || 1,
                })
              }
              className="w-24"
            />
          </div>
        </div>
      ))}
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <h3 className="text-base font-medium mb-4">Training Content Files</h3>

      <div
        {...getRootProps()}
        className={`min-h-32 border-2 border-dashed rounded-lg flex items-center justify-center flex-col gap-2 p-6 cursor-pointer transition-colors ${
          formData.contentFiles.length > 0
            ? 'bg-blue-50 border-blue-300'
            : 'bg-gray-50 border-gray-300 hover:bg-gray-100'
        }`}
      >
        <input {...getInputProps()} />
        <Upload className="w-8 h-8 text-gray-400" />
        <div className="text-center">
          <p className="text-sm font-medium text-gray-700">
            {formData.contentFiles.length > 0
              ? `${formData.contentFiles[0].name}`
              : 'Upload training content file'}
          </p>
          <p className="text-xs text-gray-500">
            Drag and drop a file here, or click to select
          </p>
          <p className="text-xs text-gray-400 mt-1">
            Supports PDF, DOC, DOCX, MP4, AVI, MOV
          </p>
        </div>
      </div>

      {formData.contentFiles.length > 0 && (
        <div className="mt-4 space-y-2">
          <Label className="text-sm font-medium">Selected File:</Label>
          <div className="flex flex-wrap gap-2">
            <FileCard key={0} file={formData.contentFiles[0]} />
          </div>
        </div>
      )}
    </div>
  );

  const renderStep4 = () => (
    <div className="space-y-6">
      <h3 className="text-base font-medium mb-4">Training Module Preview</h3>

      <div className="bg-gray-50 rounded-lg p-6 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <span className="font-medium">Title:</span>
            <p className="text-gray-700">{formData.title}</p>
          </div>
          <div>
            <span className="font-medium">Training Type:</span>
            <p className="text-gray-700">
              {
                contentTypes.find((t) => t.value === formData.training_type)
                  ?.label
              }
            </p>
          </div>

          <div>
            <span className="font-medium">Category:</span>
            <p className="text-gray-700">
              {
                categories?.records?.find(
                  (c: any) => c.id === formData.category,
                )?.name
              }
            </p>
          </div>
        </div>

        {formData.description && (
          <div>
            <span className="font-medium">Description:</span>
            <p className="text-gray-700">{formData.description}</p>
          </div>
        )}

        {formData.applicableRoles.length > 0 && (
          <div>
            <span className="font-medium">Applicable Roles:</span>
            <div className="flex flex-wrap gap-1 mt-1">
              {formData.applicableRoles.map((role) => (
                <span
                  key={role}
                  className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm"
                >
                  {availableRoles.find((r) => r.value === role)?.label}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>

      {formData.includeQuiz && formData.questions.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-6">
          <h4 className="font-medium mb-4">
            Quiz Questions ({formData.questions.length})
          </h4>
          <div className="mb-2">
            <span className="font-medium">Passing Score: </span>
            <span className="text-gray-700">
              {formData.passing_score_in_percent}%
            </span>
          </div>
        </div>
      )}

      {formData.contentFiles.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-6">
          <h4 className="font-medium mb-4">Content Files</h4>
          <div className="space-y-2">
            {formData.contentFiles.map((file, index) => (
              <div key={index} className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">{file.name}</span>
                <span className="text-xs text-gray-400">
                  ({(file.size / 1024 / 1024).toFixed(2)} MB)
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  const renderNavigationButtons = () => {
    return (
      <div className="flex justify-between items-center pt-6 border-t border-gray-200">
        <div className="flex gap-3">
          <SecondaryButton
            text="Save as Draft"
            size="medium"
            onClick={() => handleSubmit(true)}
            isLoading={isLoading}
          />
        </div>
        <div className="flex gap-3">
          {activeTab < 3 ? (
            <PrimaryButton
              text="Next"
              size="medium"
              onClick={async () => {
                const isValid = await validateCurrentTab();
                if (isValid) {
                  let nextTab = activeTab + 1;
                  // Skip quiz tab if going forward and quiz is not included
                  if (nextTab === 1 && !formData.includeQuiz) {
                    nextTab = 2;
                  }
                  setActiveTab(nextTab);
                }
              }}
            />
          ) : (
            <PrimaryButton
              text="Create & Publish"
              size="medium"
              onClick={() => handleSubmit(false)}
              isLoading={isLoading}
            />
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Training Module</DialogTitle>
          </DialogHeader>

          <div className="mt-4">
            <div className="border-b border-white-300 mb-6">
              <Tabs
                tabsData={tabsData}
                activeTab={activeTab}
                setActiveTab={handleTabChange}
                tabGroupName="training-creation"
              />
            </div>

            <div className="min-h-[400px]">
              {activeTab === 0 && renderStep1()}
              {activeTab === 1 && formData.includeQuiz && renderStep2()}
              {activeTab === 2 && renderStep3()}
              {activeTab === 3 && renderStep4()}
            </div>

            {renderNavigationButtons()}
          </div>
        </DialogContent>
      </Dialog>

      {/* Confirmation Modal */}
      {/* <Dialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <DialogContent className="max-w-md">
          <div className="text-center py-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Check className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-lg font-medium mb-2">
              Training Module Created!
            </h3>
            <p className="text-gray-600">
              Your training module has been successfully created.
            </p>
          </div>
        </DialogContent>
      </Dialog> */}
    </>
  );
};

export default CreateTrainingModal;
