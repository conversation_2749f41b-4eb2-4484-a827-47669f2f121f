import {
  EditIcon,
  Upload,
  AlertTriangle,
  Play,
  Bell,
  Calendar1,
} from 'lucide-react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';

import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import Tabs from '@/components/common/tabs';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import Loader from '@/components/common/loader';
import Breadcrumb from '@/components/common/breadcrumb';
import SideBarWrapper from '@/components/common/sidebar/layout';
import DeleteButton from '@/components/common/button/deleteButton';
import { formatDate } from '@/utils/time';
import { DetailsText, DetailsTextNew } from '@/components/common/infoDetail';
import CreateEmployeeModal from '@/components/people/modals/createEmployeeModal';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import axios from 'axios';
import { FileCard } from '../common/fileCard';
import { useDropzone } from 'react-dropzone';
import { renameFileWithExtension } from '@/utils/renameFile';
import { toast } from 'react-toastify';
import LinkButton from '../common/button/linkButton';
import { handleDownloadDocument } from '@/utils/download';
import DocumentViewModal from '../common/modals/documentViewModal';
import DocumentIcon from '@/assets/outline/document';
import Calendar from '../common/calendar';
import moment from 'moment';
import { transformList } from '@/utils/general';
import { removeEmptyFields } from '@/utils/removeEmptyFields';
import { hasAccess } from '@/utils/roleAccessConfig';
import { AccessActions } from '@/constants/access';

const accepted_file_types: any = {
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
    '.docx',
  ],
  'application/msword': ['.doc'],
  'image/jpeg': ['.jpeg', '.jpg'],
  'image/png': ['.png'],
};

const EmployeeView = () => {
  const { accessToken, user } = useAuthStore();
  const { deleteData, isLoading: deleteDataLoading } = useDelete();
  const { setIsLoading } = useAuthStore((state) => state);
  const { putData, isLoading } = usePut();

  const router = useRouter();
  const param = useParams();

  const [editUser, setEditUser] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<number>(0);
  const [assignSkillModal, setAssignSkillModal] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadDocumentModal, setUploadDocumentModal] = useState(false);
  const [addedFiles, setAddedFiles] = useState<File[]>([]);
  const [markAsLeavingModal, setMarkAsLeavingModal] = useState(false);
  const [dateOfLeaving, setDateOfLeaving] = useState('');

  // Form states for assign skill modal
  const [skillName, setSkillName] = useState('');
  const [skillCategory, setSkillCategory] = useState('');
  const [skillLevel, setSkillLevel] = useState('');

  // Form states for upload document modal
  const [documentName, setDocumentName] = useState('');

  // Mock data for new tabs
  const mockTrainingData = [
    {
      id: 't1',
      title: 'Safety Protocols Training',
      description:
        'Comprehensive training on workplace safety protocols and emergency procedures.',
      status: 'Completed',
      completedDate: '2024-01-15',
      score: 95,
      dueDate: '2024-01-10',
    },
    {
      id: 't2',
      title: 'Quality Management Systems',
      description:
        'Learn about ISO 9001 standards and quality management best practices.',
      status: 'In Progress',
      progress: 60,
      dueDate: '2024-02-15',
    },
    {
      id: 't3',
      title: 'Emergency Response Procedures',
      description:
        'Training on emergency evacuation procedures and first aid basics.',
      status: 'Assigned',
      assignedDate: '2024-01-20',
      dueDate: '2024-01-15', // Overdue date
    },
  ];

  const mockSkillsData = [
    {
      id: 's1',
      name: 'Quality Management Systems',
      category: 'Quality',
      currentLevel: 'L3 - Proficient',
      targetLevel: 'L4 - Advanced',
      lastAssessed: '2024-01-10',
    },
    {
      id: 's2',
      name: 'Safety Protocols',
      category: 'Safety',
      currentLevel: 'L4 - Advanced',
      targetLevel: 'L5 - Expert',
      lastAssessed: '2024-01-05',
    },
    {
      id: 's3',
      name: 'Statistical Process Control',
      category: 'Quality',
      currentLevel: 'L2 - Basic',
      targetLevel: 'L3 - Proficient',
      lastAssessed: '2023-12-15',
    },
  ];

  const {
    data: userData,
    isLoading: userLoading,
    reFetch: userReFetch,
  } = useFetch<{
    record: any;
  }>(accessToken, `employees/${param?.employeeId}`, {});

  const {
    data: documentsData,
    isLoading: documentsDataLoading,
    reFetch: documentsDataReFetch,
  } = useFetch<{
    records: any;
  }>(accessToken, `employees/${param?.employeeId}/documents`, {});

  const { postData } = usePost();
  const {
    putData: updateUserActive,
    isLoading: updateUserActiveLoading,
    response: updateUserActiveResponse,
    error: updateUserActiveError,
  } = usePut();

  const {
    putData: updateUserAdmin,
    isLoading: updateUserAdminLoading,
    response: updateUserAdminResponse,
    error: updateUserAdminError,
  } = usePut();

  const breadcrumbData = [
    {
      name: 'People',
      link: '/people',
    },
    {
      name: 'Directory ',
      link: '/people/directory',
    },
    {
      name: `${userData?.record?.name}`,
      link: '#',
    },
  ];

  const loadingData =
    userLoading || updateUserAdminLoading || updateUserActiveLoading;

  const tabsData = [
    { name: 'Overview', textColor: 'text-dark-100' },
    { name: 'Training', textColor: 'text-dark-100' },
    { name: 'Skills', textColor: 'text-dark-100' },
    { name: 'Documents', textColor: 'text-dark-100' },
  ];

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      setAddedFiles([acceptedFiles[0]]);
    }
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: accepted_file_types,
    multiple: false,
  });

  const removeFile = () => {
    setAddedFiles([]);
  };

  const handleDownload = (filePath: any, fileName: any) => {
    if (accessToken && filePath) {
      handleDownloadDocument(accessToken, filePath, fileName, 1);
    }
  };

  const renderOverviewTab = () => (
    <div className="flex border border-grey-100 bg-white items-start justify-between p-2 rounded-lg">
      <div className="p-2 flex flex-col gap-3">
        <DetailsTextNew
          label="Employee ID"
          value={userData?.record.employee_id || '-'}
        />
        <DetailsTextNew
          label="Date of Joining"
          value={formatDate(userData?.record.date_of_joining, false) || '-'}
        />
        <DetailsText
          label="Departments"
          value={
            userData?.record?.departments?.length &&
            userData?.record?.departments?.length > 0
              ? userData?.record?.departments
              : '-'
          }
          newLine={true}
          multiValue={
            userData?.record?.departments?.length &&
            userData?.record?.departments?.length > 0
              ? true
              : false
          }
        />
      </div>
      <div className="p-2 flex flex-col gap-3">
        <DetailsTextNew
          label="Job Title"
          value={userData?.record.job_title.name || '-'}
        />
        <DetailsTextNew
          label="Supervisor"
          value={userData?.record.supervisor?.name || '-'}
        />
        <DetailsText
          label="Processes"
          value={
            userData?.record?.processes?.length &&
            userData?.record?.processes?.length > 0
              ? userData?.record?.processes
              : '-'
          }
          newLine={true}
          multiValue={
            userData?.record?.processes?.length &&
            userData?.record?.processes?.length > 0
              ? true
              : false
          }
        />
      </div>
      <div className="p-2 flex flex-col gap-3">
        <DetailsTextNew label="Email" value={userData?.record.email || '-'} />
        <DetailsTextNew label="Status" value={userData?.record.status || '-'} />
        {userData?.record?.date_of_leaving && (
          <DetailsTextNew
            label="Date of Leaving"
            value={userData?.record?.date_of_leaving || '-'}
          />
        )}
      </div>
      <div className="flex items-center gap-3">
        <SecondaryButton
          size="medium"
          icon={<EditIcon color="#016366" className="h-5 w-5" />}
          onClick={() => setEditUser(true)}
          text="Edit"
        />
        <Dialog>
          <DialogTrigger asChild>
            <DeleteButton text="Mark as Leaving" />
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Mark Employee as Leaving</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 mt-4">
              <div className="p-4 border border-grey-100 bg-grey-50 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-grey-300">
                    Employee
                  </span>
                  <span className="text-base font-medium text-dark-300">
                    {userData?.record?.name}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-grey-300">ID</span>
                  <span className="text-base font-medium text-dark-300">
                    {userData?.record?.employee_id}
                  </span>
                </div>
              </div>

              <div>
                <Label
                  htmlFor="date_of_leaving"
                  className="text-base font-medium leading-6 text-dark-100"
                >
                  Date of Leaving<span className="text-red-200">*</span>
                </Label>
                <Calendar
                  selectedDate={dateOfLeaving}
                  onDateChange={(date) => {
                    if (date) {
                      setDateOfLeaving(
                        moment(date as string).format('YYYY-MM-DD'),
                      );
                    } else {
                      setDateOfLeaving('');
                    }
                  }}
                  allowPastDates={false}
                  className="mt-2"
                />
              </div>

              {hasAccess(AccessActions.IsPeopleAdmin, user) && (
                <div className="flex justify-end gap-3 pt-4">
                  <SecondaryButton
                    size="medium"
                    text="Cancel"
                    onClick={() => {
                      setMarkAsLeavingModal(false);
                      setDateOfLeaving('');
                    }}
                  />
                  <PrimaryButton
                    size="medium"
                    text="Mark as Leaving"
                    onClick={handleMarkAsLeaving}
                    disabled={!dateOfLeaving}
                    isLoading={updateUserActiveLoading}
                  />
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date();
  };

  const renderTrainingTab = () => (
    <div className="space-y-4">
      <div className="grid gap-4">
        {mockTrainingData.map((training) => (
          <div
            key={training.id}
            className={`border bg-white p-4 rounded-lg border-grey-100
            `}
          >
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="font-medium text-dark-300">
                    {training.title}
                  </h4>
                  {training.status === 'Completed' && (
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                      Completed
                    </span>
                  )}
                  {isOverdue(training.dueDate) &&
                    training.status !== 'Completed' && (
                      <div className="flex items-center gap-1 bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs font-medium">
                        <AlertTriangle size={12} />
                        Overdue
                      </div>
                    )}
                </div>
                <p className="text-sm text-grey-300 mb-2">
                  {training.description}
                </p>
              </div>

              <div className="flex flex-col gap-2 ml-4">
                {training.status !== 'Completed' && (
                  <>
                    <PrimaryButton
                      size="small"
                      text="Continue"
                      icon={<Play size={16} />}
                      iconPosition="left"
                    />
                    <SecondaryButton
                      size="small"
                      text="Extend Due Date"
                      icon={<Calendar1 size={16} />}
                      iconPosition="left"
                    />
                    <SecondaryButton
                      size="small"
                      text="Send Reminder"
                      icon={<Bell size={16} />}
                      iconPosition="left"
                    />
                  </>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const handleUploadDocument = async (e?: React.FormEvent) => {
    e?.preventDefault?.();

    if (addedFiles.length === 0 || isUploading) {
      return;
    }

    setIsUploading(true);
    try {
      const file = addedFiles[0];
      const renamedFile = renameFileWithExtension(documentName, file);

      const formData = new FormData();
      formData.append('file', file, renamedFile.name ?? renamedFile);

      const baseUrl = process.env.NEXT_PUBLIC_URL!;
      const productVersion = process.env.NEXT_PUBLIC_VERSION!;
      const url = `${baseUrl}/${productVersion}/file/upload?document_for=people_hub&sub_path=/employees/${
        param?.employeeId
      }/${Date.now()}/documents`;

      const orgId =
        typeof window !== 'undefined'
          ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
          : null;

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${accessToken}`,
          ...(orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
        },
      };

      const res = await axios.post(url, formData, config);

      if (res?.data) {
        const payload = {
          attachment_for: 'employee',
          record_id: param?.employeeId,
          attachments: [
            {
              file_path: res.data.file_path,
              file_extension: res.data.file_ext,
            },
          ],
        };
        await postData(accessToken as string, 'attachments', payload);
      }
      setAddedFiles([]);
      setDocumentName('');
      documentsDataReFetch();
      toast.success('Document uploaded successfully');
    } catch (err) {
      console.error('upload failed', err);
      toast.error('Failed to upload document');
    } finally {
      setIsUploading(false);
      setUploadDocumentModal(false);
    }
  };

  const renderSkillsTab = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        {mockSkillsData.map((skill) => (
          <div
            key={skill.id}
            className="border border-grey-100 bg-white py-2 px-4 rounded-lg"
          >
            <div className="flex justify-between items-center">
              <h4 className="font-medium text-dark-300">{skill.name}</h4>
              <span className="text-sm font-medium text-grey-300">
                {skill.currentLevel}
              </span>
            </div>
          </div>
        ))}
      </div>
      {mockSkillsData?.length === 0 && (
        <div className="border border-grey-100 bg-white p-4 rounded-lg text-center text-grey-300">
          No skills assigned
        </div>
      )}
    </div>
  );

  const renderDocumentsTab = () => (
    <div className="space-y-4">
      <div className="grid gap-4">
        {documentsData?.records?.map((doc: any) => (
          <div
            key={doc.id}
            className="border border-grey-100 bg-white py-2 px-4 rounded-lg"
          >
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-medium text-dark-300 mb-1">
                  {doc.file_path.split('/').pop() || ''}
                </h4>
                <p className="text-sm text-grey-300 leading-5 font-medium">
                  <strong>Type:</strong> {doc.file_extension}
                </p>
              </div>
              <div>
                <Dialog>
                  <DialogTrigger asChild>
                    {['pdf', 'docx', 'doc', 'jpg', 'png', 'jpeg'].includes(
                      doc.file_extension,
                    ) && <LinkButton text="View" size="medium" />}
                  </DialogTrigger>
                  <DocumentViewModal
                    title={doc.file_path.split('/').pop() || ''}
                    filePath={doc?.file_path}
                    extension={
                      doc.file_extension as
                        | 'html'
                        | 'pdf'
                        | 'png'
                        | 'jpeg'
                        | 'jpg'
                    }
                    dialogClass="min-w-[95%]"
                  />
                </Dialog>
                <LinkButton
                  text="Download"
                  size="medium"
                  onClick={() =>
                    handleDownload(
                      doc?.file_path,
                      doc.file_path.split('/').pop(),
                    )
                  }
                />
              </div>
            </div>
          </div>
        ))}
        {documentsData?.records?.length === 0 && (
          <div className="border border-grey-100 bg-white p-4 rounded-lg text-center text-grey-300">
            No documents found
          </div>
        )}
      </div>
    </div>
  );

  const handleMarkAsLeaving = async () => {
    if (!dateOfLeaving) {
      toast.error('Please select a date of leaving');
      return;
    }

    try {
      const payload = {
        employee_id: userData?.record?.employee_id,
        name: userData?.record?.name,
        email: userData?.record?.email,
        departments: userData?.record?.departments
          ? userData?.record?.departments
              .map((option: any) => option?.value)
              .filter((value: any) => value != null && value !== '')
          : [],
        processes: userData?.record?.processes
          ? userData?.record?.processes
              .map((option: any) => option?.value)
              .filter((value: any) => value != null && value !== '')
          : [],
        job_title: userData?.record?.job_title,
        status: 'Resigned',
        date_of_joining: userData?.record?.date_of_joining,
        supervisor_id: userData?.record?.supervisor?.id || '',
        date_of_leaving: dateOfLeaving,
      };

      await putData(
        accessToken as string,
        `employees/${param?.employeeId}`,
        removeEmptyFields(payload),
      );

      toast.success('Employee marked as leaving successfully');
      setMarkAsLeavingModal(false);
      setDateOfLeaving('');
      userReFetch();
    } catch (error) {
      toast.error('Failed to mark employee as leaving');
    }
  };

  return loadingData ? (
    <Loader className="h-[80vh]" />
  ) : (
    <SideBarWrapper>
      <div className="flex flex-col flex-1">
        <div className="my-5">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-2.5">
            {userData?.record && userData?.record.name}
          </div>
        </div>

        <div className="flex-1">
          <div className=" mb-6">
            <Tabs
              tabsData={tabsData}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              tabGroupName="employee-details"
              tabRightSideElement={
                activeTab === 3 ? (
                  <Dialog
                    open={uploadDocumentModal}
                    onOpenChange={setUploadDocumentModal}
                  >
                    <DialogTrigger asChild>
                      <PrimaryButton
                        size="medium"
                        text="Upload Document"
                        icon={<Upload size={20} />}
                        iconPosition="left"
                      />
                    </DialogTrigger>
                    <DialogContent className="max-w-lg">
                      <DialogHeader>
                        <DialogTitle>Upload Document</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4 mt-4">
                        <div>
                          <Label
                            htmlFor="document-name"
                            className="text-base font-medium text-dark-100"
                          >
                            Document Name
                          </Label>
                          <Input
                            id="document-name"
                            placeholder="Enter document name"
                            value={documentName}
                            onChange={(e) => setDocumentName(e.target.value)}
                            className="mt-2"
                          />
                        </div>

                        <div>
                          <div className="mt-2">
                            <div
                              {...getRootProps()}
                              className={`min-h-32 border-2 border-dashed rounded-lg flex items-center justify-center flex-col gap-2 p-6 cursor-pointer transition-colors ${
                                addedFiles.length > 0
                                  ? 'bg-blue-50 border-blue-300'
                                  : 'bg-gray-50 border-gray-300 hover:bg-gray-100'
                              }`}
                            >
                              <input {...getInputProps()} />
                              <Upload className="w-8 h-8 text-gray-400" />
                              <div className="text-center">
                                <p className="text-sm font-medium text-gray-700">
                                  {addedFiles.length > 0
                                    ? addedFiles[0].name
                                    : 'Upload training content file'}
                                </p>
                                <p className="text-xs text-gray-500">
                                  Drag and drop a file here, or click to select
                                </p>
                                <p className="text-xs text-gray-400 mt-1">
                                  Supports PDF, DOC, DOCX, JPG, PNG, JPEG
                                </p>
                              </div>
                            </div>

                            {addedFiles.length > 0 && (
                              <div className="mt-4 space-y-2">
                                <Label className="text-sm font-medium">
                                  Selected File:
                                </Label>
                                <FileCard
                                  filepath={addedFiles[0].name}
                                  file_extension={addedFiles[0].type}
                                  handleDelete={() => removeFile()}
                                />
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex justify-end gap-3 pt-4">
                          <SecondaryButton
                            size="medium"
                            text="Cancel"
                            onClick={() => setUploadDocumentModal(false)}
                          />
                          <PrimaryButton
                            size="medium"
                            text="Upload"
                            onClick={handleUploadDocument}
                            disabled={!documentName || addedFiles.length === 0}
                            isLoading={isUploading}
                          />
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                ) : undefined
              }
            />
          </div>

          <div className="min-h-[400px]">
            {activeTab === 0 && renderOverviewTab()}
            {activeTab === 1 && renderTrainingTab()}
            {activeTab === 2 && renderSkillsTab()}
            {activeTab === 3 && renderDocumentsTab()}
          </div>
        </div>

        <Dialog open={editUser} onOpenChange={setEditUser}>
          {editUser && (
            <CreateEmployeeModal
              edit={editUser}
              employeeData={userData?.record}
              setOpenEdit={setEditUser}
              reFetch={userReFetch}
            />
          )}
        </Dialog>
      </div>
    </SideBarWrapper>
  );
};

export default EmployeeView;
