import React, { useState, useMemo } from 'react';
import { Dialog } from '@/components/common/dialog';
import AssignSkillModal from './assignSkillModal';
import { Input } from '@/components/common/input';
import {
  getSkillLevelColor,
  getSkillLevelName,
} from '@/utils/skillLevelColors';
import SkillLevelLegend from '@/components/skills/skillLevelLegend';

// Type definitions
interface Employee {
  id: string;
  name: string;
}

interface Skill {
  id: string;
  name: string;
}

interface SkillCategory {
  category: string;
  skills: Skill[];
}

interface SkillLevel {
  employeeId: string;
  skillId: string;
  level: string;
}

interface SkillRecord {
  id: string;
  skill_id: string;
  skill_name: string;
  employee_id: string;
  employee_name: string;
  category_id: string;
  category_name: string;
  current_skill_level_id: string;
  current_skill_level_name: string;
}

// New mock data structure based on the provided API response format
// const mockSkillsData = {
//   records: [
//     {
//       id: '6eb1099d-e0b8-4b13-98a4-2768b4d11b92',
//       skill_id: '565fb4e3-3d23-4e18-a5e9-f04f787745ae',
//       skill_name: 'Risk Assessment',
//       employee_id: '506e9ef4-7bff-4f43-b2b6-36ed3f73a03e',
//       employee_name: 'Vishnu Tripathi',
//       category_id: 'af5168cf-2f1d-4087-8d81-dbbf5e9fb20f',
//       category_name: 'Safety',
//       current_skill_level_id: 'fc51d72d-368a-4b40-bf91-cff6468f03b7',
//       current_skill_level_name: 'L1',
//     },
//     {
//       id: '099c9671-0fd9-464e-8bc0-9a980a82d7d7',
//       skill_id: 'b0d034cf-60c2-4276-9cd5-03a2f7cac7b5',
//       skill_name: 'Quality Management Systems',
//       employee_id: 'c4236740-a813-4a32-bfaf-2335b0bfb622',
//       employee_name: 'Damoder Gundu',
//       category_id: 'a668219f-1168-48ad-92db-7e4a4468a877',
//       category_name: 'Aerospace',
//       current_skill_level_id: '8acd3607-3454-43b0-b58a-5370b91d7762',
//       current_skill_level_name: 'L2',
//     },
//   ],
// };

// Transform the data into the format expected by the component
const transformSkillsData = (data: {
  records: SkillRecord[];
}): {
  employees: Employee[];
  skillCategories: SkillCategory[];
  skillLevels: SkillLevel[];
} => {
  const employees = new Map<string, Employee>();
  const skillCategories = new Map<
    string,
    { category: string; skills: Map<string, Skill> }
  >();
  const skillLevels: SkillLevel[] = [];

  data.records.forEach((record) => {
    // Collect unique employees
    employees.set(record.employee_id, {
      id: record.employee_id,
      name: record.employee_name,
    });

    // Collect unique categories and skills
    if (!skillCategories.has(record.category_name)) {
      skillCategories.set(record.category_name, {
        category: record.category_name,
        skills: new Map<string, Skill>(),
      });
    }

    const category = skillCategories.get(record.category_name)!;
    category.skills.set(record.skill_id, {
      id: record.skill_id,
      name: record.skill_name,
    });

    // Collect skill levels
    skillLevels.push({
      employeeId: record.employee_id,
      skillId: record.skill_id,
      level: record.current_skill_level_name,
    });
  });

  return {
    employees: Array.from(employees.values()),
    skillCategories: Array.from(skillCategories.values()).map((cat) => ({
      category: cat.category,
      skills: Array.from(cat.skills.values()),
    })),
    skillLevels,
  };
};

export default function SkillsMatrix({
  skillsData,
  skillCategories,
  skillLevels,
  employeesData,
}: {
  skillsData: any[];
  skillCategories: any[];
  skillLevels: any[];
  employeesData: any[];
}) {
  console.log('Matrix Data', skillsData);
  console.log('skillCategories', skillCategories);
  console.log('skillLevels', skillLevels);
  console.log('employeesData', employeesData);

  const {
    employees: mockEmployees,
    skillCategories: mockSkillCategories,
    skillLevels: mockSkillLevels,
  }: {
    employees: Employee[];
    skillCategories: SkillCategory[];
    skillLevels: SkillLevel[];
  } = transformSkillsData(skillsData);

  const [selected, setSelected] = useState<{
    employeeId: string;
    skillId: string;
  } | null>(null);
  const [search, setSearch] = useState('');

  const getLevel = (employeeId: string, skillId: string) => {
    const found = mockSkillLevels.find(
      (l) => l.employeeId === employeeId && l.skillId === skillId,
    );
    return found ? found.level : '+';
  };

  const getLevelDisplay = (level: string) => {
    if (level === '+') return { level: '+', name: 'Not Assigned' };
    // Keep the "L" prefix for display
    return { level, name: getSkillLevelName(level.replace('L', '')) };
  };

  const getLevelStyles = (level: string) => {
    if (level === '+') {
      return 'bg-white-100 text-grey-300 border border-dashed border-grey-200';
    }
    const levelNumber = level.replace('L', '');
    const colors = getSkillLevelColor(levelNumber);
    return `${colors.bg} text-white font-medium`;
  };

  const filteredEmployees = useMemo((): Employee[] => {
    return mockEmployees.filter((e) =>
      e.name.toLowerCase().includes(search.toLowerCase()),
    );
  }, [search]);

  const filteredSkillCategories = useMemo((): SkillCategory[] => {
    if (!search.trim()) return mockSkillCategories;
    return mockSkillCategories.map((cat) => ({
      ...cat,
      skills: cat.skills.filter((s: Skill) =>
        s.name.toLowerCase().includes(search.toLowerCase()),
      ),
    }));
  }, [search]);

  return (
    <div className="space-y-5">
      <div className="mt-4">
        <SkillLevelLegend skillLevels={skillLevels} />
      </div>
      <div className="flex flex-col sm:flex-row justify-between mb-4 gap-2 mt-4">
        <div className="flex items-center justify-between relative w-full">
          <Input
            placeholder="Search by employee or skill"
            className="w-[40vw] bg-white flex-auto rounded-lg border border-grey-100 py-2.5 px-3 text-black outline-none transition focus:border-primary active:border-primary"
            onChange={(e) => {
              setSearch(e.target.value);
            }}
            value={search}
          />
        </div>
      </div>

      {/* Desktop/Tablet Table */}
      <div className="hidden md:block rounded-lg border border-white-200 bg-white-100 shadow-sm max-h-[70vh] overflow-auto">
        <table className="min-w-full text-base border-collapse table-auto">
          <thead>
            {/* Header row: sticky at top */}
            <tr className="bg-white-200 text-left border border-grey-100 sticky top-0 z-40">
              {/* Skills column header (auto width) */}
              <th className="px-3 py-3 sticky left-0 z-50 bg-white-200">
                <span className="text-base font-semibold text-grey-300 truncate">
                  Skills
                </span>
              </th>
              {/* Employee columns */}
              {filteredEmployees.map((emp) => (
                <th
                  key={emp.id}
                  className="px-3 py-3 bg-white-200 border-b border-grey-100 min-w-[120px]"
                  title={emp.name}
                >
                  <span className="text-base font-semibold text-grey-300 truncate block max-w-[10rem] lg:max-w-[14rem]">
                    {emp.name}
                  </span>
                </th>
              ))}
            </tr>
          </thead>

          <tbody>
            {filteredSkillCategories.map((cat) => (
              <React.Fragment key={cat.category}>
                {/* Sticky Category Row */}
                <tr className="border-t border-white-200">
                  {/* Sticky first column */}
                  <td
                    className="px-3 py-2 text-dark-300 font-semibold sticky left-0 z-40 bg-white-50"
                    style={{ top: '48px' }}
                  >
                    {cat.category}
                  </td>
                  <td
                    colSpan={filteredEmployees.length}
                    className="bg-white-50"
                    style={{ top: '48px' }}
                  />
                </tr>

                {/* Skills rows */}
                {cat.skills.map((skill: Skill) => (
                  <tr key={skill.id} className="border-t border-white-200">
                    {/* Sticky skill name */}
                    <td className="p-2 sticky left-0 z-30 bg-white-100">
                      <span className="inline-block rounded-full bg-primary-100 text-primary-400 px-3 py-1 text-sm font-medium whitespace-nowrap">
                        {skill.name}
                      </span>
                    </td>

                    {/* Employee skill levels */}
                    {filteredEmployees.map((emp) => {
                      const level = getLevel(emp.id, skill.id);
                      const levelDisplay = getLevelDisplay(level);
                      const levelStyles = getLevelStyles(level);
                      return (
                        <td
                          key={emp.id}
                          className="p-2 cursor-pointer hover:bg-white-50"
                          onClick={() =>
                            setSelected({
                              employeeId: emp.id,
                              skillId: skill.id,
                            })
                          }
                          title={`${emp.name} - ${levelDisplay.name}`}
                        >
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${levelStyles}`}
                          >
                            {levelDisplay.level}
                          </span>
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile List */}
      <div className="md:hidden space-y-3">
        {filteredSkillCategories.map((cat) => (
          <div
            key={cat.category}
            className="rounded-lg border border-white-200 bg-white-100 shadow-sm"
          >
            <div className="px-3 py-2 bg-white-50 text-dark-300 font-semibold rounded-t-lg sticky top-0 z-10">
              {cat.category}
            </div>
            <div className="divide-y divide-white-200">
              {cat.skills.map((skill: Skill) => (
                <div key={skill.id} className="p-3 space-y-2">
                  <div>
                    <span className="inline-block rounded-full bg-primary-100 text-primary-400 px-3 py-1 text-xs font-medium">
                      {skill.name}
                    </span>
                  </div>
                  <div className="flex gap-2 overflow-x-auto pb-1">
                    {filteredEmployees.map((emp) => {
                      const level = getLevel(emp.id, skill.id);
                      const levelDisplay = getLevelDisplay(level);
                      const levelStyles = getLevelStyles(level);
                      return (
                        <button
                          key={emp.id}
                          className="flex-shrink-0 inline-flex items-center gap-2 px-3 py-2 rounded-lg border border-white-200 bg-white-100 hover:bg-white-50"
                          onClick={() =>
                            setSelected({
                              employeeId: emp.id,
                              skillId: skill.id,
                            })
                          }
                          title={`${emp.name} - ${levelDisplay.name}`}
                        >
                          <span className="max-w-[8rem] truncate text-sm text-dark-200">
                            {emp.name}
                          </span>
                          <span
                            className={`px-2 py-0.5 rounded-full text-sm ${levelStyles}`}
                          >
                            {levelDisplay.level}
                          </span>
                        </button>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {selected && (
        <Dialog open={!!selected} onOpenChange={() => setSelected(null)}>
          <AssignSkillModal
            employeeId={selected.employeeId}
            skillId={selected.skillId}
            onClose={() => setSelected(null)}
          />
        </Dialog>
      )}
    </div>
  );
}
