import React, { useState } from 'react';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { Label } from '@/components/common/label';
import { Input } from '@/components/common/input';
import { Textarea } from '@/components/common/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import {
  getSkillLevelColor,
  getSkillLevelName,
} from '@/utils/skillLevelColors';
import next from 'next';
import moment from 'moment';
import Calendar from '@/components/common/calendar';

const levels = ['L1', 'L2', 'L3', 'L4', 'L5'];
const assessmentMethods = [
  { value: 'Interview', label: 'Interview' },
  { value: 'Test', label: 'Test' },
  { value: 'Observation', label: 'Observation' },
  { value: 'Portfolio', label: 'Portfolio Review' },
  { value: 'Certification', label: 'Certification' },
];

function AssignSkillModal({
  employeeId,
  skillId,
  onClose,
}: {
  employeeId: string;
  skillId: string;
  onClose: () => void;
}) {
  const [currentLevel, setCurrentLevel] = useState('');
  const [targetLevel, setTargetLevel] = useState('');
  const [assessor, setAssessor] = useState('');
  const [method, setMethod] = useState('');
  const [evidence, setEvidence] = useState('');
  const [notes, setNotes] = useState('');
  const [nextReviewDate, setNextReviewDate] = useState('');

  return (
    <DialogContent className="max-w-xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>Assign Skill</DialogTitle>
      </DialogHeader>

      <div className="space-y-5 mt-2">
        <div className="flex flex-col gap-2.5">
          <Label
            htmlFor="current_level"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Current Level
          </Label>
          <div className="flex flex-wrap gap-2">
            {levels.map((lvl) => {
              const levelNumber = lvl.replace('L', '');
              const colors = getSkillLevelColor(levelNumber);
              return (
                <button
                  key={lvl}
                  onClick={() => setCurrentLevel(lvl)}
                  className={`px-2.5 py-1.5 rounded-full border text-sm font-medium transition-colors ${
                    currentLevel === lvl
                      ? `${colors.bg} text-white border-${colors.bg.replace(
                          'bg-',
                          '',
                        )}`
                      : ` border ${colors.border} hover:${colors.bg} hover:text-black`
                  }`}
                  title={getSkillLevelName(levelNumber)}
                >
                  {lvl}
                </button>
              );
            })}
          </div>
        </div>

        <div className="flex flex-col gap-2.5">
          <Label
            htmlFor="target_level"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Target Level
          </Label>
          <div className="flex flex-wrap gap-2">
            {levels.map((lvl) => {
              const levelNumber = lvl.replace('L', '');
              const colors = getSkillLevelColor(levelNumber);
              return (
                <button
                  key={lvl}
                  onClick={() => setTargetLevel(lvl)}
                  className={`px-2.5 py-1.5 rounded-full border text-sm font-medium transition-colors ${
                    targetLevel === lvl
                      ? `${colors.bg} text-white border-${colors.bg.replace(
                          'bg-',
                          '',
                        )}`
                      : ` border ${colors.border} hover:${colors.bg} hover:text-black`
                  }`}
                  title={getSkillLevelName(levelNumber)}
                >
                  {lvl}
                </button>
              );
            })}
          </div>
        </div>

        <div className="flex flex-col gap-2.5">
          <Label
            htmlFor="assessor"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Assessor Name
          </Label>
          <Input
            id="assessor"
            placeholder="Enter assessor name"
            value={assessor}
            onChange={(e) => setAssessor(e.target.value)}
          />
        </div>

        <div className="flex flex-col gap-2.5">
          <Label
            htmlFor="method"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Assessment Method
          </Label>
          <Select value={method} onValueChange={setMethod}>
            <SelectTrigger>
              <SelectValue placeholder="Select assessment method" />
            </SelectTrigger>
            <SelectContent>
              {assessmentMethods.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex flex-col gap-2.5">
          <Label
            htmlFor="evidence"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Evidence
          </Label>
          <Textarea
            id="evidence"
            placeholder="Describe the evidence supporting this skill level"
            rows={3}
            value={evidence}
            onChange={(e) => setEvidence(e.target.value)}
          />
        </div>

        <div className="flex flex-col gap-2.5">
          <Label
            htmlFor="notes"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Notes
          </Label>
          <Textarea
            id="notes"
            placeholder="Additional notes or comments"
            rows={3}
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
          />
        </div>

        <div>
          <Label
            htmlFor="date_of_leaving"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Next Review Date
          </Label>
          <Calendar
            selectedDate={nextReviewDate}
            onDateChange={(date) => {
              if (date) {
                setNextReviewDate(moment(date as string).format('YYYY-MM-DD'));
              } else {
                setNextReviewDate('');
              }
            }}
            allowPastDates={false}
            className="mt-2"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-6 border-t mt-6">
        <SecondaryButton text="Cancel" size="medium" onClick={onClose} />
        <PrimaryButton text="Save" size="medium" onClick={onClose} />
      </div>
    </DialogContent>
  );
}

export default AssignSkillModal;
